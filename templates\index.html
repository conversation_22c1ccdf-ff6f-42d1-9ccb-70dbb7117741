﻿{% extends "base.html" %}

{% block title %}仪表盘 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}仪表盘{% endblock %}

{% block extra_css %}
<style>
    /* 统计卡片样式 */
    .stats-card {
        background: white;
        border-radius: 16px;
        padding: 1.75rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(226, 232, 240, 0.8);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    .stats-card:hover::before {
        opacity: 1;
    }
    
    .stats-icon {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1.25rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .stats-icon::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        transform: scale(0);
        border-radius: 50%;
        transition: transform 0.6s ease-out;
    }
    
    .stats-card:hover .stats-icon::after {
        transform: scale(2.5);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.25rem;
        line-height: 1;
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    .stats-label {
        color: #718096;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: relative;
        display: inline-block;
    }
    
    .stats-change {
        font-size: 0.85rem;
        font-weight: 600;
        margin-top: 0.75rem;
        padding: 0.35rem 0.75rem;
        border-radius: 9999px;
        display: inline-block;
    }
    
    .stats-change.positive {
        background-color: rgba(72, 187, 120, 0.1);
        color: #38a169;
    }
    
    .stats-change.negative {
        background-color: rgba(245, 101, 101, 0.1);
        color: #e53e3e;
    }
    
    .stats-change i {
        font-size: 0.75rem;
    }

    /* 上传区域样式 */
    .upload-section {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .upload-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-bottom: none;
    }

    .upload-area {
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius-lg);
        padding: 3rem 2rem;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
        background: var(--gray-50);
        margin: 2rem;
    }

    .upload-area:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
    }

    .upload-area.dragover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: scale(1.02);
    }

    .upload-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
    }

    .analysis-types {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 2rem;
    }

    .analysis-type-card {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        padding: 1.25rem;
        cursor: pointer;
        transition: var(--transition);
        text-align: center;
        background: white;
    }

    .analysis-type-card:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .analysis-type-card.selected {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        box-shadow: var(--shadow-md);
    }

    .analysis-type-icon {
        width: 48px;
        height: 48px;
        background: var(--gray-100);
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.25rem;
        color: var(--gray-600);
        transition: var(--transition);
    }

    .analysis-type-card.selected .analysis-type-icon {
        background: var(--primary-color);
        color: white;
    }

    /* 最近文件样式 */
    .recent-files-section {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .recent-files-header {
        background: var(--gray-50);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .recent-files-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .file-item {
        padding: 1rem 2rem;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
        cursor: pointer;
    }

    .file-item:hover {
        background: var(--gray-50);
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.125rem;
        margin-right: 1rem;
    }

    .file-pdf {
        background: #fee2e2;
        color: #dc2626;
    }

    .file-image {
        background: #dbeafe;
        color: #2563eb;
    }

    /* 文件列表展开样式 */
    .file-list-section {
        margin-top: 1rem;
        border-top: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.3s ease-out;
    }

    .file-list-section.expanded {
        max-height: 400px;
    }

    .file-list-header {
        padding: 0.75rem 1.25rem;
        background: var(--gray-100);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: between;
        align-items: center;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
    }

    .file-list-content {
        max-height: 300px;
        overflow-y: auto;
    }

    .file-list-item {
        padding: 0.75rem 1.25rem;
        border-bottom: 1px solid var(--gray-200);
        transition: var(--transition);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .file-list-item:hover {
        background: white;
    }

    .file-list-item:last-child {
        border-bottom: none;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-name {
        font-size: 0.875rem;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        font-weight: 500;
    }

    .file-meta {
        font-size: 0.75rem;
        color: var(--gray-500);
        display: flex;
        gap: 1rem;
    }

    .file-actions {
        display: flex;
        gap: 0.5rem;
    }

    .file-action-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-600);
        cursor: pointer;
        transition: var(--transition);
    }

    .file-action-btn:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .expand-toggle {
        margin-top: 0.75rem;
        padding: 0.5rem;
        background: var(--gray-100);
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        text-align: center;
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    .expand-toggle:hover {
        background: var(--gray-200);
    }

    .expand-toggle i {
        transition: transform 0.3s ease;
    }

    .expand-toggle.expanded i {
        transform: rotate(180deg);
    }

    /* 文件列表空状态样式 */
    .file-list-empty {
        padding: 2rem 1rem;
        text-align: center;
        color: var(--gray-500);
    }

    .file-list-empty i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        opacity: 0.5;
    }

    /* 文件列表加载状态 */
    .file-list-loading {
        padding: 1.5rem;
        text-align: center;
        color: var(--gray-500);
    }

    .file-list-loading .spinner-border {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.5rem;
    }

    /* 状态指示器样式 */
    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 1rem;
        font-weight: 500;
    }

    .status-indicator.status-pending {
        background: #fef3c7;
        color: #92400e;
    }

    .status-indicator.status-processing {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-indicator.status-completed {
        background: #d1fae5;
        color: #065f46;
    }

    .status-indicator.status-failed {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-indicator.status-cancelled {
        background: var(--gray-100);
        color: var(--gray-600);
    }

    /* 文件操作按钮样式优化 */
    .file-action-btn.btn-primary {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .file-action-btn.btn-outline {
        background: transparent;
        border-color: var(--gray-300);
        color: var(--gray-600);
    }

    .file-action-btn.btn-outline:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .file-list-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .file-actions {
            width: 100%;
            justify-content: flex-end;
        }

        .file-meta {
            flex-direction: column;
            gap: 0.25rem;
        }
    }

    /* 进度条样式 */
    .progress-container {
        display: none;
        margin: 2rem;
        padding: 1.5rem;
        background: var(--gray-50);
        border-radius: var(--border-radius);
    }

    .progress-container.show {
        display: block;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .stats-number {
            font-size: 2rem;
        }

        .upload-area {
            padding: 2rem 1rem;
            margin: 1rem;
        }

        .analysis-types {
            grid-template-columns: 1fr;
            margin: 1rem;
        }
    }
    
    /* 最近执行操作样式 */
    .activity-item {
        transition: var(--transition);
        padding: 10px 15px;
        border-left: 3px solid;
        margin-bottom: 8px;
        white-space: normal; /* 确保文本会自动换行 */
        width: 100%; /* 确保宽度不超出容器 */
        box-sizing: border-box; /* 确保padding不会增加宽度 */
    }
    
    .activity-item:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-md) !important;
    }
    
    .activity-icon {
        transition: var(--transition);
    }
    
    .activity-item:hover .activity-icon {
        transform: scale(1.1);
    }
    
    /* 快速操作按钮样式 */
    .quick-action-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        text-decoration: none;
        color: var(--gray-700);
        transition: var(--transition);
        height: 100%;
    }
    
    .quick-action-btn:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        color: var(--primary-color);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: var(--shadow-sm);
    }
    
    .quick-action-icon {
        width: 28px; /* 减小图标尺寸 */
        height: 28px; /* 减小图标尺寸 */
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.85rem; /* 减小图标字体大小 */
        transition: var(--transition);
    }
    
    .quick-action-btn:hover .quick-action-icon {
        background: var(--primary-color);
        color: white !important;
    }
    
    .operation-list {
        max-height: 400px; /* 减小一点最大高度，使内容更紧凑 */
        overflow-y: auto;
        overflow-x: hidden; /* 隐藏水平滚动条 */
        padding: 0 10px; /* 添加适当的水平内边距 */
        scrollbar-width: thin; /* 细滚动条 */
    }
    
    /* 添加滚动条样式 */
    .operation-list::-webkit-scrollbar {
        width: 5px;
    }
    
    .operation-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    .operation-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }
    
    .operation-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* 活动项样式 */
    .activity-item {
        margin-bottom: 0.5rem !important; /* 减少项目之间的间距 */
        padding: 0.5rem 0.75rem !important; /* 减少内部填充 */
        border-radius: 0 !important; /* 移除圆角 */
        border-width: 0 0 0 3px !important; /* 只保留左边框 */
        box-shadow: none !important; /* 移除阴影 */
    }

    /* 定义各种颜色变量 */
    :root {
        --primary-bg: rgba(37, 99, 235, 0.1);
        --info-bg: rgba(6, 182, 212, 0.1);
        --success-bg: rgba(16, 185, 129, 0.1);
        --warning-bg: rgba(245, 158, 11, 0.1);
        --danger-bg: rgba(239, 68, 68, 0.1);
        --secondary-bg: rgba(156, 163, 175, 0.1);
    }

    /* 统计卡片内容布局 */
    .stats-content {
        position: relative;
        z-index: 2;
    }
    
    /* 背景大图标 */
    .stats-bg-icon {
        position: absolute;
        bottom: -10px;
        right: -10px;
        font-size: 6rem;
        opacity: 0.04;
        transform: rotate(-10deg);
        transition: transform 0.5s ease;
        z-index: 1;
    }
    
    .stats-card:hover .stats-bg-icon {
        transform: rotate(0deg) scale(1.1);
    }

    /* 美化的快速操作按钮样式 */
    .quick-action-button {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 12px;
        transition: all 0.3s ease;
        margin-bottom: 12px;
        background: var(--white);
        border: 1px solid var(--gray-200);
        box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        color: var(--gray-700);
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .quick-action-button:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 5px;
        transition: all 0.3s ease;
    }

    .quick-action-button.primary:before { background-color: var(--primary-color); }
    .quick-action-button.warning:before { background-color: var(--warning-color); }
    .quick-action-button.info:before { background-color: var(--info-color); }
    .quick-action-button.secondary:before { background-color: var(--secondary-color); }

    .quick-action-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        color: var(--gray-900);
    }

    .quick-action-button:hover:before {
        width: 100%;
        opacity: 0.1;
    }

    .quick-action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 10px;
        margin-right: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .quick-action-button.primary .quick-action-icon { 
        background-color: var(--primary-bg); 
        color: var(--primary-color);
    }

    .quick-action-button.warning .quick-action-icon { 
        background-color: var(--warning-bg); 
        color: var(--warning-color);
    }

    .quick-action-button.info .quick-action-icon { 
        background-color: var(--info-bg); 
        color: var(--info-color);
    }

    .quick-action-button.secondary .quick-action-icon { 
        background-color: var(--secondary-bg); 
        color: var(--secondary-color);
    }

    .quick-action-button:hover .quick-action-icon {
        transform: scale(1.1);
    }

    /* 分析类型卡片样式 */
    .analysis-type-item {
        display: flex;
        align-items: center; /* 确保垂直居中对齐 */
        justify-content: flex-start; /* 左对齐更自然 */
        padding: 12px 20px;
        border-radius: 16px;
        background-color: #fff;
        border: none;
        transition: all 0.3s ease;
        margin: 6px;
        text-decoration: none;
        color: var(--gray-800);
        flex-grow: 1;
        flex-basis: 0;
        min-width: 160px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 避免内容溢出 */
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .analysis-type-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.1);
        color: var(--primary-color);
        text-decoration: none;
        background-color: #fafafa;
    }

    .analysis-type-icon {
        display: inline-flex; /* 改用inline-flex以确保更好的对齐 */
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 10px;
        margin-right: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        flex-shrink: 0; /* 防止图标缩小 */
        position: relative; /* 添加相对定位 */
        top: 0; /* 确保不会有意外的偏移 */
        transform: translateY(0); /* 重置任何变换 */
    }

    .analysis-type-item:hover .analysis-type-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .analysis-type-text {
        display: inline-block; /* 显式设置为inline-block */
        vertical-align: middle; /* 确保垂直居中 */
    }

    .analysis-type-item span {
        font-size: 0.95rem;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: 0.3px;
        line-height: 1; /* 调整行高为1，确保精确对齐 */
        padding-top: 1px; /* 轻微上移，以实现视觉上的居中 */
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<!-- 欢迎区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2 text-gray-900">
                    {% if current_user.is_authenticated %}
                        欢迎回来，{{ current_user.username }}！
                    {% else %}
                        欢迎使用智能文档分析系统
                    {% endif %}
                </h1>
                <p class="text-muted mb-0">开始您的智能文档分析之旅，体验AI驱动的高效识别</p>
            </div>
            <div class="text-end">
                <div class="text-muted small">
                    <i class="bi bi-calendar3 me-1"></i>
                    <span id="currentDate"></span>
                </div>
                <div class="text-muted small">
                    <i class="bi bi-clock me-1"></i>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);">
                <i class="bi bi-file-earmark-text"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number" id="totalFiles">240</div>
                <div class="stats-label">总文件数</div>
                <div class="stats-change positive" id="filesChange">
                    <i class="bi bi-arrow-up-short"></i>+12% 本月
                </div>
            </div>
            <div class="stats-bg-icon">
                <i class="bi bi-file-earmark-text"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number" id="todayProcessed">9</div>
                <div class="stats-label">今日处理</div>
                <div class="stats-change positive" id="processedChange">
                    <i class="bi bi-arrow-up-short"></i>+8% 昨日
                </div>
            </div>
            <div class="stats-bg-icon">
                <i class="bi bi-check-circle"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number" id="pendingReviews">124</div>
                <div class="stats-label">待复核</div>
                <div class="stats-change" id="pendingChange">
                    <i class="bi bi-dash"></i>无变化
                </div>
            </div>
            <div class="stats-bg-icon">
                <i class="bi bi-clock-history"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="bi bi-graph-up"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number" id="accuracyRate">80.7%</div>
                <div class="stats-label">识别准确率</div>
                <div class="stats-change positive" id="accuracyChange">
                    <i class="bi bi-arrow-up-short"></i>****% 本月
                </div>
            </div>
            <div class="stats-bg-icon">
                <i class="bi bi-graph-up"></i>
            </div>
        </div>
    </div>
</div>

<!-- 业务类型分布饼形图和业务类型趋势图 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart-fill me-2"></i>
                    业务类型分布
                </h5>
                <!-- 添加饼图的时间范围选择器 -->
                <div class="d-flex align-items-center">
                    <label class="me-2 mb-0 small">时间范围：</label>
                    <select class="form-select form-select-sm" id="pieChartTimeRange" style="width: auto;">
                        <option value="7">近7天</option>
                        <option value="30" selected>近一月</option>
                        <option value="180">近半年</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <canvas id="typeDistributionChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up-arrow me-2"></i>
                    业务类型趋势
                </h5>
                <!-- 添加趋势图的时间范围选择器 -->
                <div class="d-flex align-items-center">
                    <label class="me-2 mb-0 small">时间范围：</label>
                    <select class="form-select form-select-sm" id="trendChartTimeRange" style="width: auto;">
                        <option value="7">近7天</option>
                        <option value="30" selected>近一月</option>
                        <option value="180">近半年</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <canvas id="typeTrendChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 业务类型统计图表 -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card modern-card h-100"> <!-- 添加h-100类使卡片高度占满 -->
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity me-2"></i>
                    最近执行操作
                </h5>
            </div>
            <div class="card-body p-0 pt-1"> <!-- 减少padding，使内容更紧凑 -->
                <div class="row m-0" id="recentOperations"> <!-- 移除row的margin -->
                    <!-- 动态生成最近执行操作 -->
                    <div class="col-12 p-0"> <!-- 移除列的padding -->
                        <div class="text-center p-4 text-muted operation-loading">
                            <i class="bi bi-hourglass me-2"></i>
                            加载中...
                        </div>
                        <div class="operation-empty d-none">
                            <div class="text-center p-4 text-muted">
                                <i class="bi bi-inbox display-4 mb-3"></i>
                                <p class="mb-0">暂无最近操作记录</p>
                            </div>
                        </div>
                        <div class="operation-list d-none">
                            <!-- 操作记录将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4 mb-4">
        <div class="card modern-card h-100"> <!-- 添加h-100类使卡片高度占满 -->
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body py-2 px-3"> <!-- 减少内边距 -->
                <div class="d-grid gap-2"> <!-- 减少按钮间间距 -->

                    {% if current_user.role == 'admin' %}
                    <a href="{{ url_for('main.model_config') }}" class="quick-action-button info">
                        <div class="quick-action-icon">
                            <i class="bi bi-cpu"></i>
                        </div>
                        <div class="quick-action-text">
                            <span class="fw-medium">模型配置</span>
                        </div>
                    </a>
                    {% endif %}
                    <a href="{{ url_for('main.help') }}" class="quick-action-button secondary">
                        <div class="quick-action-icon">
                            <i class="bi bi-question-circle"></i>
                        </div>
                        <div class="quick-action-text">
                            <span class="fw-medium">帮助中心</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支持的分析类型 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card modern-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-check me-2"></i>
                    支持的分析类型
                </h5>
            </div>
            <div class="card-body py-2 px-3">
                <div class="d-flex justify-content-between gap-2" id="analysisTypesQuick">
                    <!-- 将由JS动态生成分析类型链接 -->
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="text-muted ms-2 small">加载分析类型...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let selectedAnalysisType = '';
    let selectedFiles = [];

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializePage();
        loadDashboardData();
        loadAnalysisTypes();
        loadAnalysisTypesQuick(); // 加载快速操作区的分析类型
        loadRecentFiles();
        
        // 加载类型统计区域的数据
        loadTypeStats(30); // 默认加载30天的数据
        
        // 初始化图表和设置事件监听
        loadChartLibrary();
        
        loadRecentOperations();
        setupEventListeners();
        updateDateTime();
        setInterval(updateDateTime, 1000); // 每秒更新时间
    });
    
    // 设置时间范围选择器事件
    function setupTimeRangeSelectors() {
        // 设置饼图的时间范围选择器
        const pieChartSelector = document.getElementById('pieChartTimeRange');
        if (pieChartSelector) {
            pieChartSelector.addEventListener('change', function() {
                const days = parseInt(this.value) || 30;
                loadTypeDistributionChart(days);
            });
        }
        
        // 设置趋势图的时间范围选择器
        const trendChartSelector = document.getElementById('trendChartTimeRange');
        if (trendChartSelector) {
            trendChartSelector.addEventListener('change', function() {
                const days = parseInt(this.value) || 30;
                loadTypeTrendChart(days);
            });
        }
    }
    
    // 加载Chart.js库
    function loadChartLibrary() {
        if (window.Chart) {
            console.log('Chart.js已加载');
            initializeCharts();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
        script.onload = function() {
            console.log('Chart.js加载成功');
            initializeCharts();
        };
        script.onerror = function() {
            console.error('Chart.js加载失败，尝试备用CDN');
            const backupScript = document.createElement('script');
            backupScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js';
            backupScript.onload = function() {
                console.log('Chart.js备用CDN加载成功');
                initializeCharts();
            };
            backupScript.onerror = function() {
                console.error('Chart.js加载失败');
            };
            document.head.appendChild(backupScript);
        };
        document.head.appendChild(script);
    }
    
    // 初始化图表
    function initializeCharts() {
        // 获取饼图时间范围
        const pieChartSelector = document.getElementById('pieChartTimeRange');
        const pieDays = pieChartSelector ? parseInt(pieChartSelector.value) || 30 : 30;
        
        // 获取趋势图时间范围
        const trendChartSelector = document.getElementById('trendChartTimeRange');
        const trendDays = trendChartSelector ? parseInt(trendChartSelector.value) || 30 : 30;
        
        // 加载图表并传递时间范围参数
        loadTypeDistributionChart(pieDays);
        loadTypeTrendChart(trendDays);
        
        // 设置时间范围选择器事件
        setupTimeRangeSelectors();
    }
    
    // 渲染业务类型分布图表
    function loadTypeDistributionChart(days = 30) {
        console.log(`正在加载业务类型分布数据，时间范围: ${days}天...`);
        API.get(`/api/dashboard/type-stats?days=${days}`)
            .then(response => {
                console.log('业务类型分布数据API响应:', response);
                if (response.success && response.data) {
                    renderTypeDistributionChart(response.data);
                } else {
                    console.error('加载类型分布统计失败:', response.message || '未知错误');
                    // 使用模拟数据渲染
                    const mockData = generateTypeDistributionMockData(days);
                    renderTypeDistributionChart(mockData);
                }
            })
            .catch(error => {
                console.error('加载类型分布统计失败:', error);
                // 使用模拟数据渲染
                const mockData = generateTypeDistributionMockData(days);
                renderTypeDistributionChart(mockData);
            });
    }
    
    // 生成饼图模拟数据
    function generateTypeDistributionMockData(days) {
        // 基础数据
        const baseData = [
            { type: '期货账户/开户文件解析', count: 245, percentage: 35.2 },
            { type: '理财产品说明书', count: 189, percentage: 27.1 },
            { type: '券商账户计息变更', count: 134, percentage: 19.2 },
            { type: '账户开户场景', count: 78, percentage: 11.2 },
            { type: '宁银理财费用变更', count: 32, percentage: 4.6 },
            { type: '非标交易确认单解析', count: 18, percentage: 2.7 }
        ];
        
        // 根据时间范围调整数据
        let adjustedData;
        if (days <= 7) {
            // 近7天数据量较少
            adjustedData = baseData.map(item => {
                const factor = 0.3 + Math.random() * 0.3; // 30%-60%的基础数据
                return {
                    type: item.type,
                    count: Math.round(item.count * factor)
                };
            });
        } else if (days <= 30) {
            // 近一月数据量适中
            adjustedData = baseData.map(item => {
                const factor = 0.6 + Math.random() * 0.2; // 60%-80%的基础数据
                return {
                    type: item.type,
                    count: Math.round(item.count * factor)
                };
            });
        } else {
            // 近半年完整数据
            adjustedData = [...baseData];
        }
        
        // 重新计算百分比
        const totalCount = adjustedData.reduce((sum, item) => sum + item.count, 0);
        return adjustedData.map(item => ({
            type: item.type,
            count: item.count,
            percentage: totalCount > 0 ? Math.round((item.count / totalCount * 100) * 10) / 10 : 0
        }));
    }
    
    // 渲染业务类型分布图表
    function renderTypeDistributionChart(data) {
        const ctx = document.getElementById('typeDistributionChart');
        if (!ctx || !window.Chart) return;
        
        // 如果已有图表实例，先销毁
        if (window.typeDistributionChartInstance) {
            window.typeDistributionChartInstance.destroy();
        }
        
        // 标签映射函数
        function mapTypeLabel(label) {
            const labelMap = {
                'futures_account': '期货账户/开户文件解析',
                'wealth_management': '理财产品说明书',
                'broker_interest': '券商账户计息变更',
                'account_opening': '账户开户场景',
                'ningyin_fee': '宁银理财费用变更',
                'non_standard_trade': '非标交易确认单解析'
            };
            return labelMap[label] || label;
        }
        
        // 从数据中提取标签和数据值
        const labels = data.map(item => mapTypeLabel(item.type));
        const values = data.map(item => item.count);
        
        // 图表颜色
        const backgroundColors = [
            'rgba(99, 102, 241, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(6, 182, 212, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(107, 114, 128, 0.8)'
        ];
        
        // 创建图表实例
        window.typeDistributionChartInstance = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: backgroundColors,
                    borderColor: 'white',
                    borderWidth: 2,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#4B5563',
                            font: {
                                size: 13
                            },
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.getDatasetMeta(0).total || 1;
                                const percentage = Math.round(value / total * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // 渲染业务类型趋势图表
    function loadTypeTrendChart(days = 30) {
        console.log(`正在加载业务类型趋势数据，时间范围: ${days}天...`);
        API.get(`/api/dashboard/trend-stats?days=${days}`)
            .then(response => {
                console.log('业务类型趋势数据API响应:', response);
                if (response.success && response.data) {
                    renderTypeTrendChart(response.data);
                } else {
                    console.error('加载趋势统计失败:', response.message || '未知错误');
                    // 使用模拟数据渲染
                    const mockData = generateMockTrendData(days);
                    renderTypeTrendChart(mockData);
                }
            })
            .catch(error => {
                console.error('加载趋势统计失败:', error);
                // 使用模拟数据渲染
                const mockData = generateMockTrendData(days);
                renderTypeTrendChart(mockData);
            });
    }
    
    // 根据时间范围生成模拟趋势数据
    function generateMockTrendData(days) {
        const now = new Date();
        let labels = [];
        let dataPoints = [];
        
        // 根据时间范围生成不同粒度的标签和数据点数量
        if (days <= 7) {  // 近7天，每天一个数据点
            // 从当前日期向前推6天，总共7天
            for (let i = 6; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                labels.push(formatDate(date, 'MM-DD'));
            }
            dataPoints = 7;
        } else if (days <= 30) {  // 近一月，每周一个数据点
            // 从当前日期向前推27天，分成4周
            for (let i = 3; i >= 0; i--) {
                const weekStart = new Date(now);
                weekStart.setDate(weekStart.getDate() - (i * 7 + 6));
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);
                labels.push(`${formatDate(weekStart, 'MM-DD')}~${formatDate(weekEnd, 'MM-DD')}`);
            }
            dataPoints = 4;
        } else {  // 近半年，每月一个数据点
            // 计算当前月份和前5个月的月份标签，总共6个月
            const today = new Date();
            const currentMonth = today.getMonth();
            const currentYear = today.getFullYear();
            
            for (let i = 5; i >= 0; i--) {
                let month = currentMonth - i;
                let year = currentYear;
                
                // 处理月份为负数的情况
                if (month < 0) {
                    month += 12;
                    year -= 1;
                }
                
                const monthDate = new Date(year, month, 1);
                labels.push(formatDate(monthDate, 'YYYY-MM'));
            }
            dataPoints = 6;
        }
        
        // 生成模拟数据
        const baseValues = {
            '开户文件解析': [23, 35, 42, 50, 45, 50],
            '理财产品说明书': [18, 25, 30, 35, 40, 41],
            '券商账户计息变更': [12, 15, 20, 25, 30, 32],
            '账户开户场景': [14, 18, 22, 26, 28, 30],
            '宁银理财费用变更': [8, 10, 15, 18, 20, 22],
            '非标交易确认单解析': [5, 7, 3, 1, 2, 0]
        };
        
        // 调整数据点数量
        const datasets = {};
        Object.keys(baseValues).forEach(key => {
            if (dataPoints <= 6) {
                // 如果数据点少于基础值，截取后面的部分
                datasets[key] = baseValues[key].slice(-dataPoints);
            } else {
                // 如果数据点多于基础值，使用插值算法扩展
                datasets[key] = interpolateValues(baseValues[key], dataPoints);
            }
        });
        
        return {
            labels: labels,
            datasets: datasets
        };
    }
    
    // 日期格式化函数
    function formatDate(date, format) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        if (format === 'YYYY-MM') {
            return `${year}-${month}`;
        } else if (format === 'MM-DD') {
            return `${month}-${day}`;
        }
        return `${year}-${month}-${day}`;
    }
    
    // 简单的线性插值算法
    function interpolateValues(values, targetLength) {
        if (values.length === targetLength) return values;
        if (targetLength <= 1) return [values[0]];
        
        const result = [];
        const factor = (values.length - 1) / (targetLength - 1);
        
        for (let i = 0; i < targetLength; i++) {
            const position = i * factor;
            const index = Math.floor(position);
            const nextIndex = Math.min(index + 1, values.length - 1);
            const fraction = position - index;
            
            const value = values[index] + (values[nextIndex] - values[index]) * fraction;
            result.push(Math.round(value));
        }
        
        return result;
    }
    
    // 渲染业务类型趋势图表
    function renderTypeTrendChart(data) {
        const ctx = document.getElementById('typeTrendChart');
        if (!ctx || !window.Chart) return;
        
        // 如果已有图表实例，先销毁
        if (window.typeTrendChartInstance) {
            window.typeTrendChartInstance.destroy();
        }
        
        // 标签映射函数
        function mapTypeLabel(label) {
            const labelMap = {
                'futures_account': '期货账户/开户文件解析',
                'wealth_management': '理财产品说明书',
                'broker_interest': '券商账户计息变更',
                'account_opening': '账户开户场景',
                'ningyin_fee': '宁银理财费用变更',
                'non_standard_trade': '非标交易确认单解析'
            };
            return labelMap[label] || label;
        }
        
        // 颜色配置
        const typeColors = {
            // 中文键映射
            '开户文件解析': {
                borderColor: 'rgba(99, 102, 241, 1)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)'
            },
            '理财产品说明书': {
                borderColor: 'rgba(16, 185, 129, 1)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)'
            },
            '券商账户计息变更': {
                borderColor: 'rgba(245, 158, 11, 1)',
                backgroundColor: 'rgba(245, 158, 11, 0.1)'
            },
            '账户开户场景': {
                borderColor: 'rgba(239, 68, 68, 1)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)'
            },
            '宁银理财费用变更': {
                borderColor: 'rgba(6, 182, 212, 1)',
                backgroundColor: 'rgba(6, 182, 212, 0.1)'
            },
            '非标交易确认单解析': {
                borderColor: 'rgba(139, 92, 246, 1)',
                backgroundColor: 'rgba(139, 92, 246, 0.1)'
            },
            // 标准分析类型配置
            'futures_account': {
                borderColor: 'rgba(99, 102, 241, 1)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)'
            },
            'wealth_management': {
                borderColor: 'rgba(16, 185, 129, 1)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)'
            },
            'broker_interest': {
                borderColor: 'rgba(245, 158, 11, 1)',
                backgroundColor: 'rgba(245, 158, 11, 0.1)'
            },
            'account_opening': {
                borderColor: 'rgba(239, 68, 68, 1)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)'
            },
            'ningyin_fee': {
                borderColor: 'rgba(6, 182, 212, 1)',
                backgroundColor: 'rgba(6, 182, 212, 0.1)'
            },
            'non_standard_trade': {
                borderColor: 'rgba(139, 92, 246, 1)',
                backgroundColor: 'rgba(139, 92, 246, 0.1)'
            }
        };
        
        // 准备数据集
        const datasets = [];
        const mappedDatasets = {};
        
        // 先对数据集标签进行映射处理
        for (const [typeName, values] of Object.entries(data.datasets)) {
            const mappedName = mapTypeLabel(typeName);
            if (!mappedDatasets[mappedName]) {
                mappedDatasets[mappedName] = values;
            } else {
                // 如果存在多个键映射到同一个显示名称，则合并数据
                mappedDatasets[mappedName] = mappedDatasets[mappedName].map((val, idx) => val + (values[idx] || 0));
            }
        }
        
        // 处理映射后的数据集
        for (const [typeName, values] of Object.entries(mappedDatasets)) {
            const colorConfig = typeColors[typeName] || {
                borderColor: `rgba(${Math.random()*255}, ${Math.random()*255}, ${Math.random()*255}, 1)`,
                backgroundColor: `rgba(${Math.random()*255}, ${Math.random()*255}, ${Math.random()*255}, 0.1)`
            };
            
            datasets.push({
                label: typeName,
                data: values,
                borderColor: colorConfig.borderColor,
                backgroundColor: colorConfig.backgroundColor,
                borderWidth: 2,
                tension: 0.3,
                fill: true
            });
        }
        
        // 创建图表实例
        window.typeTrendChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#4B5563',
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(156, 163, 175, 0.1)'
                        },
                        ticks: {
                            color: '#6B7280',
                            precision: 0
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    // 初始化页面
    function initializePage() {
        // 添加页面加载动画
        document.body.classList.add('fade-in');

        // 设置统计卡片动画
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('slide-up');
            }, index * 100);
        });
    }

    // 更新日期时间
    function updateDateTime() {
        const now = new Date();
        const dateOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };

        const dateElement = document.getElementById('currentDate');
        const timeElement = document.getElementById('currentTime');

        if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('zh-CN', dateOptions);
        }
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('zh-CN', timeOptions);
        }
    }

    // 加载仪表盘数据
    function loadDashboardData() {
        // 获取统计数据
        loadDashboardStats();
        
        // 加载图表库
        loadChartLibrary();
        
        // 初始化时间范围选择器
        initializeTimeRangeSelector();
    }
    
    // 初始化时间范围选择器
    function initializeTimeRangeSelector() {
        const timeRangeSelector = document.getElementById('chartTimeRange');
        if (!timeRangeSelector) return;
        
        // 设置默认值为30天（一个月）
        timeRangeSelector.value = '30';
    }
    
    // 加载仪表盘统计数据
    function loadDashboardStats() {
        API.get('/api/dashboard/stats')
            .then(response => {
                if (response.success) {
                    updateStatsCards(response.data);
                } else {
                    console.error('加载统计数据失败:', response.message);
                }
            })
            .catch(error => {
                console.error('加载统计数据失败:', error);
                // 显示模拟数据
                updateStatsCards({
                    total_files: 240,
                    today_processed: 9,
                    pending_reviews: 124,
                    accuracy_rate: 80.7,
                    files_change: {value: '+12%', period: '本月'},
                    processed_change: {value: '+8%', period: '昨日'},
                    pending_change: {value: '无变化', period: ''},
                    accuracy_change: {value: '****%', period: '本月'}
                });
            });
    }

    // 更新统计卡片
    function updateStatsCards(data) {
        const elements = {
            totalFiles: document.getElementById('totalFiles'),
            todayProcessed: document.getElementById('todayProcessed'),
            pendingReviews: document.getElementById('pendingReviews'),
            accuracyRate: document.getElementById('accuracyRate'),
            filesChange: document.getElementById('filesChange'),
            processedChange: document.getElementById('processedChange'),
            pendingChange: document.getElementById('pendingChange'),
            accuracyChange: document.getElementById('accuracyChange')
        };

        // 使用动画更新数字
        if (elements.totalFiles) {
            animateNumber(elements.totalFiles, 0, data.total_files || 0, 1000);
        }
        if (elements.todayProcessed) {
            animateNumber(elements.todayProcessed, 0, data.today_processed || 0, 800);
        }
        if (elements.pendingReviews) {
            animateNumber(elements.pendingReviews, 0, data.pending_reviews || 0, 600);
        }
        if (elements.accuracyRate) {
            animateNumber(elements.accuracyRate, 0, data.accuracy_rate || 0, 1200, '%');
        }
        
        // 更新变化指标的样式和内容
        if (elements.filesChange) {
            updateChangeIndicator(elements.filesChange, data.files_change || {value: '+12%', period: '本月'});
        }
        if (elements.processedChange) {
            updateChangeIndicator(elements.processedChange, data.processed_change || {value: '+8%', period: '昨日'});
        }
        if (elements.pendingChange) {
            updateChangeIndicator(elements.pendingChange, data.pending_change || {value: '无变化', period: ''});
        }
        if (elements.accuracyChange) {
            updateChangeIndicator(elements.accuracyChange, data.accuracy_change || {value: '****%', period: '本月'});
        }
    }
    
    // 更新变化指标
    function updateChangeIndicator(element, change) {
        if (!element) return;
        
        // 如果change是字符串，转换为对象
        if (typeof change === 'string') {
            change = {value: change, period: ''};
        }
        
        const value = change.value || '0%';
        const period = change.period || '';
        
        let icon = 'bi-dash';
        let className = '';
        
        if (value.startsWith('+')) {
            icon = 'bi-arrow-up-short';
            className = 'positive';
        } else if (value.startsWith('-')) {
            icon = 'bi-arrow-down-short';
            className = 'negative';
        }
        
        element.className = `stats-change ${className}`;
        element.innerHTML = `<i class="${icon}"></i>${value} ${period}`;
    }

    // 数字动画效果
    function animateNumber(element, start, end, duration, suffix = '') {
        const startTime = performance.now();
        const difference = end - start;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = start + (difference * easeOutQuart);

            if (suffix === '%') {
                element.textContent = current.toFixed(1) + suffix;
            } else {
                element.textContent = Math.floor(current).toLocaleString() + suffix;
            }

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // 加载分析类型
    function loadAnalysisTypes() {
        const analysisTypes = [
            { id: 'futures_account', name: '期货账户/开户文件解析', icon: 'bi-graph-up', description: '期货账户开户文件相关文档分析' },
            { id: 'wealth_management', name: '理财产品说明书', icon: 'bi-piggy-bank', description: '理财产品说明书解析' },
            { id: 'broker_interest', name: '券商账户计息变更', icon: 'bi-calculator', description: '券商账户计息变更文档' },
            { id: 'account_opening', name: '账户开户场景', icon: 'bi-person-plus', description: '账户开户相关文档分析' },
            { id: 'ningyin_fee', name: '宁银理财费用变更', icon: 'bi-receipt', description: '宁银理财费用变更文档' },
            { id: 'non_standard_trade', name: '非标交易确认单解析', icon: 'bi-file-text', description: '非标交易确认单解析' }
        ];

        const container = document.getElementById('analysisTypes');
        if (!container) return;

        container.innerHTML = '';

        analysisTypes.forEach(type => {
            const typeCard = document.createElement('div');
            typeCard.className = 'analysis-type-card';
            typeCard.dataset.type = type.id;

            typeCard.innerHTML = `
                <div class="analysis-type-icon">
                    <i class="bi ${type.icon}"></i>
                </div>
                <h6 class="mb-1">${type.name}</h6>
                <small class="text-muted">${type.description}</small>
                <div class="expand-toggle" data-type="${type.id}">
                    <i class="bi bi-chevron-down"></i>
                    <span>查看文件列表</span>
                </div>
                <div class="file-list-section" id="fileList_${type.id}">
                    <div class="file-list-header">
                        <span>最近上传的文件</span>
                        <span class="file-count">加载中...</span>
                    </div>
                    <div class="file-list-content" id="fileListContent_${type.id}">
                        <!-- 文件列表将在这里动态加载 -->
                    </div>
                </div>
            `;

            typeCard.addEventListener('click', (e) => {
                // 如果点击的是展开按钮，则处理展开/收起
                if (e.target.closest('.expand-toggle')) {
                    e.stopPropagation();
                    toggleFileList(type.id);
                } else {
                    // 否则选择分析类型
                    selectAnalysisType(type.id, typeCard);
                }
            });

            container.appendChild(typeCard);
        });
    }

    // 选择分析类型
    function selectAnalysisType(typeId, element) {
        // 移除其他选中状态
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 设置当前选中
        element.classList.add('selected');
        selectedAnalysisType = typeId;

        // 检查是否可以启用上传按钮
        checkUploadButton();
    }

    // 加载分析类型快速链接
    function loadAnalysisTypesQuick() {
        const container = document.getElementById('analysisTypesQuick');
        if (!container) return;

        // 获取分析类型配置
        API.get('/api/system/analysis-types')
            .then(response => {
                if (response.success) {
                    renderAnalysisTypesQuick(response.data);
                } else {
                    console.error('加载分析类型失败:', response.message);
                    // 显示模拟数据
                    renderAnalysisTypesQuick({
                        'futures_account': '期货账户/开户文件解析',
                        'wealth_management': '理财产品说明书',
                        'broker_interest': '券商账户计息变更',
                        'account_opening': '账户开户场景',
                        'ningyin_fee': '宁银理财费用变更',
                        'non_standard_trade': '非标交易确认单解析'
                    });
                }
            })
            .catch(error => {
                console.error('加载分析类型失败:', error);
                // 显示模拟数据
                renderAnalysisTypesQuick({
                    'futures_account': '期货账户/开户文件解析',
                    'wealth_management': '理财产品说明书',
                    'broker_interest': '券商账户计息变更',
                    'account_opening': '账户开户场景',
                    'ningyin_fee': '宁银理财费用变更',
                    'non_standard_trade': '非标交易确认单解析'
                });
            });
    }

    // 渲染分析类型快速链接
    function renderAnalysisTypesQuick(types) {
        const container = document.getElementById('analysisTypesQuick');
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 定义图标和颜色映射
        const iconMapping = {
            'futures_account': 'bi-graph-up-arrow',
            'wealth_management': 'bi-piggy-bank-fill',
            'broker_interest': 'bi-cash-coin',
            'account_opening': 'bi-person-plus-fill',
            'ningyin_fee': 'bi-bank2',
            'non_standard_trade': 'bi-file-earmark-text-fill'
        };

        const colorMapping = {
            'futures_account': '#4361ee',
            'wealth_management': '#4cc9f0',
            'broker_interest': '#f72585',
            'account_opening': '#ff9e00',
            'ningyin_fee': '#7209b7',
            'non_standard_trade': '#4ade80'
        };

        const bgColorMapping = {
            'futures_account': '#eef2ff',
            'wealth_management': '#e6f8ff',
            'broker_interest': '#fff0f7',
            'account_opening': '#fff4e0',
            'ningyin_fee': '#f5eeff',
            'non_standard_trade': '#e6fff0'
        };

        // 为每种分析类型创建链接
        Object.entries(types).forEach(([key, label]) => {
            const icon = iconMapping[key] || 'bi-file-earmark-text-fill';
            const color = colorMapping[key] || '#3b82f6';
            const bgColor = bgColorMapping[key] || '#f0f7ff';
            
            const typeLink = document.createElement('a');
            typeLink.href = `/document-analysis?type=${key}`;
            typeLink.className = `analysis-type-item`;
            
            // 添加透明度，使渐变效果更柔和
            const bgWithOpacity = `${bgColor}80`; // 添加50%透明度
            
            typeLink.innerHTML = `
                <div class="analysis-type-icon" style="background: linear-gradient(135deg, ${bgColor} 0%, ${bgWithOpacity} 100%); color: ${color};">
                    <i class="bi ${icon}"></i>
                </div>
                <div class="analysis-type-text">
                    <span>${label}</span>
                </div>
            `;
            
            container.appendChild(typeLink);
        });
    }

    // 切换文件列表展开/收起
    function toggleFileList(typeId) {
        const fileListSection = document.getElementById(`fileList_${typeId}`);
        const expandToggle = document.querySelector(`[data-type="${typeId}"]`);

        if (!fileListSection || !expandToggle) return;

        const isExpanded = fileListSection.classList.contains('expanded');

        if (isExpanded) {
            // 收起
            fileListSection.classList.remove('expanded');
            expandToggle.classList.remove('expanded');
            expandToggle.querySelector('span').textContent = '查看文件列表';
        } else {
            // 展开
            fileListSection.classList.add('expanded');
            expandToggle.classList.add('expanded');
            expandToggle.querySelector('span').textContent = '收起文件列表';

            // 加载文件列表
            loadFileList(typeId);
        }
    }

    // 加载指定类型的文件列表
    function loadFileList(typeId) {
        const contentElement = document.getElementById(`fileListContent_${typeId}`);
        const countElement = document.querySelector(`#fileList_${typeId} .file-count`);

        if (!contentElement) return;

        // 显示加载状态
        contentElement.innerHTML = `
            <div class="file-list-loading">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                加载中...
            </div>
        `;
        if (countElement) countElement.textContent = '加载中...';

        // 调用API获取文件记录
        API.get(`/api/records?type=${typeId}&per_page=10&file_status=active`)
            .then(response => {
                if (response.success && response.data) {
                    const records = response.data.records || [];
                    displayFileList(typeId, records);
                    if (countElement) {
                        countElement.textContent = `共 ${response.data.pagination?.total || records.length} 个文件`;
                    }
                } else {
                    contentElement.innerHTML = `
                        <div class="file-list-empty">
                            <i class="bi bi-folder2-open"></i>
                            <div>暂无文件记录</div>
                        </div>
                    `;
                    if (countElement) countElement.textContent = '0 个文件';
                }
            })
            .catch(error => {
                console.error('加载文件列表失败:', error);
                contentElement.innerHTML = `
                    <div class="file-list-empty">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                        <div class="text-danger">加载失败，请重试</div>
                    </div>
                `;
                if (countElement) countElement.textContent = '加载失败';
            });
    }

    // 显示文件列表
    function displayFileList(typeId, records) {
        const contentElement = document.getElementById(`fileListContent_${typeId}`);
        if (!contentElement) return;

        if (records.length === 0) {
            contentElement.innerHTML = `
                <div class="file-list-empty">
                    <i class="bi bi-folder2-open"></i>
                    <div>暂无文件记录</div>
                </div>
            `;
            return;
        }

        const fileListHTML = records.map(record => {
            const createdAt = new Date(record.created_at).toLocaleString('zh-CN');
            const statusText = getStatusText(record.status);
            const statusClass = getStatusClass(record.status);

            return `
                <div class="file-list-item" data-record-id="${record.id}">
                    <div class="file-info">
                        <div class="file-name" title="${record.filename}">${truncateFilename(record.filename, 30)}</div>
                        <div class="file-meta">
                            <span><i class="bi bi-calendar3"></i> ${createdAt}</span>
                            <span class="status-indicator status-${record.status}">
                                <i class="bi bi-circle-fill"></i>
                                ${statusText}
                            </span>
                        </div>
                    </div>
                    <div class="file-actions">
                        ${record.status === 'completed' ?
                            '<button class="file-action-btn btn-primary" onclick="viewAnalysisResult(' + record.id + ')">查看结果</button>' :
                            '<button class="file-action-btn btn-outline" onclick="viewRecordDetail(' + record.id + ')">查看详情</button>'
                        }
                    </div>
                </div>
            `;
        }).join('');

        contentElement.innerHTML = fileListHTML;
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    // 获取状态样式类
    function getStatusClass(status) {
        const classMap = {
            'pending': 'text-warning',
            'processing': 'text-info',
            'completed': 'text-success',
            'failed': 'text-danger',
            'cancelled': 'text-muted'
        };
        return classMap[status] || 'text-muted';
    }

    // 截断文件名
    function truncateFilename(filename, maxLength) {
        if (filename.length <= maxLength) return filename;
        const ext = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - ext.length - 4) + '...';
        return truncatedName + '.' + ext;
    }

    // 查看分析结果
    function viewAnalysisResult(recordId) {
        // 跳转到结果页面
        window.open(`/records/${recordId}`, '_blank');
    }

    // 查看记录详情
    function viewRecordDetail(recordId) {
        // 跳转到记录详情页面
        window.open(`/records/${recordId}`, '_blank');
    }

    // 设置事件监听器
    function setupEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');

        if (uploadArea && fileInput) {
            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);
        }

        if (uploadBtn) {
            uploadBtn.addEventListener('click', startUploadAndAnalysis);
        }
    }

    // 处理文件选择
    function handleFileSelect(event) {
        const files = Array.from(event.target.files);
        processSelectedFiles(files);
    }

    // 处理拖拽悬停
    function handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }

    // 处理拖拽离开
    function handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }

    // 处理文件拖拽
    function handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');

        const files = Array.from(event.dataTransfer.files);
        processSelectedFiles(files);
    }

    // 处理选中的文件
    function processSelectedFiles(files) {
        // 验证文件
        const validFiles = files.filter(file => {
            const validTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff'];
            const maxSize = 200 * 1024 * 1024; // 200MB

            if (!validTypes.includes(file.type) && !file.name.match(/\.(pdf|png|jpg|jpeg|gif|bmp|tiff)$/i)) {
                showMessage(`文件 ${file.name} 格式不支持`, 'warning');
                return false;
            }

            if (file.size > maxSize) {
                showMessage(`文件 ${file.name} 超过200MB限制`, 'warning');
                return false;
            }

            return true;
        });

        if (validFiles.length > 0) {
            // 智能分发：根据文件数量决定处理方式
            if (validFiles.length === 1) {
                // 单文件：直接上传并分析
                uploadSingleFileWithAnalysis(validFiles[0]);
            } else {
                // 多文件：询问用户是否立即分析
                showMultiFileUploadDialog(validFiles);
            }
        }
    }

    // 更新上传区域显示
    function updateUploadArea() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea || selectedFiles.length === 0) return;

        const fileCount = selectedFiles.length;
        const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
        const sizeText = formatFileSize(totalSize);

        uploadArea.innerHTML = `
            <div class="upload-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <h5 class="mb-3">已选择 ${fileCount} 个文件</h5>
            <p class="text-muted mb-2">总大小：${sizeText}</p>
            <p class="text-muted mb-0">
                <i class="bi bi-info-circle me-1"></i>
                点击重新选择文件
            </p>
        `;
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 检查上传按钮状态
    function checkUploadButton() {
        const uploadBtn = document.getElementById('uploadBtn');
        if (!uploadBtn) return;

        const canUpload = selectedAnalysisType && selectedFiles.length > 0;
        uploadBtn.disabled = !canUpload;

        if (canUpload) {
            uploadBtn.classList.remove('btn-secondary');
            uploadBtn.classList.add('btn-primary');
        } else {
            uploadBtn.classList.remove('btn-primary');
            uploadBtn.classList.add('btn-secondary');
        }
    }

    // 开始上传和分析
    function startUploadAndAnalysis() {
        if (!selectedAnalysisType || selectedFiles.length === 0) {
            showMessage('请选择分析类型和文件', 'warning');
            return;
        }

        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressPercent = document.getElementById('progressPercent');
        const uploadBtn = document.getElementById('uploadBtn');

        // 显示进度条
        if (progressContainer) {
            progressContainer.classList.add('show');
        }

        // 禁用上传按钮
        if (uploadBtn) {
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>处理中...';
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('analysis_type', selectedAnalysisType);

        selectedFiles.forEach((file, index) => {
            formData.append('files', file);
        });

        // 上传文件并自动分析
        const httpClient = getHttpClient();
        httpClient.post('/api/analysis/start', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
                if (progressEvent && progressEvent.total) {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);

                    if (progressBar) {
                        progressBar.style.width = percentCompleted + '%';
                    }
                    if (progressPercent) {
                        progressPercent.textContent = percentCompleted + '%';
                    }
                    if (progressText) {
                        if (percentCompleted < 100) {
                            progressText.textContent = `上传中... ${percentCompleted}%`;
                        } else {
                            progressText.textContent = '上传完成，正在分析...';
                        }
                    }
                }
            }
        })
        .then(response => {
            if (response.data.success) {
                const results = response.data.analysis_results || [];
                const recordIds = response.data.record_ids || [];

                if (progressText) {
                    progressText.textContent = '分析完成！';
                }

                // 显示分析结果
                if (results.length > 0) {
                    const successCount = results.filter(r => r.success).length;
                    const totalCount = results.length;

                    if (successCount === totalCount) {
                        showMessage(`成功分析 ${successCount} 个文件`, 'success');
                    } else {
                        showMessage(`成功分析 ${successCount}/${totalCount} 个文件`, 'warning');
                    }
                } else {
                    showMessage('文件上传成功', 'success');
                }

                // 跳转到记录页面或结果页面
                setTimeout(() => {
                    if (recordIds.length === 1) {
                        // 单文件，跳转到具体记录
                        window.location.href = `/records`;
                    } else {
                        // 多文件，跳转到记录列表
                        window.location.href = `/records`;
                    }
                }, 1500);
            } else {
                throw new Error(response.data.message);
            }
        })
        .catch(error => {
            console.error('上传失败:', error);
            showMessage('上传失败: ' + (error.response?.data?.message || error.message), 'error');

            // 重置界面
            resetUploadInterface();
        });
    }

    // 重置上传界面
    function resetUploadInterface() {
        const progressContainer = document.getElementById('progressContainer');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadArea = document.getElementById('uploadArea');

        if (progressContainer) {
            progressContainer.classList.remove('show');
        }

        if (uploadBtn) {
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="bi bi-upload me-2"></i>开始上传并分析';
        }

        // 重置上传区域
        if (uploadArea) {
            uploadArea.innerHTML = `
                <div class="upload-icon">
                    <i class="bi bi-cloud-upload"></i>
                </div>
                <h5 class="mb-3">拖拽文件到此处或点击选择文件</h5>
                <p class="text-muted mb-2">支持 PDF、PNG、JPG、JPEG、GIF、BMP、TIFF 格式</p>
                <p class="text-muted mb-0">
                    <i class="bi bi-info-circle me-1"></i>
                    最大文件大小：50MB，支持批量上传
                </p>
            `;
        }

        selectedFiles = [];
        checkUploadButton();
    }

    // HTTP客户端备用方案
    function getHttpClient() {
        if (window.axios) {
            return window.axios;
        }

        // 备用的fetch实现
        return {
            post: function(url, data, config = {}) {
                const options = {
                    method: 'POST',
                    ...config
                };

                if (data instanceof FormData) {
                    options.body = data;
                    // 删除Content-Type，让浏览器自动设置
                    if (options.headers && options.headers['Content-Type'] === 'multipart/form-data') {
                        delete options.headers['Content-Type'];
                    }
                } else {
                    options.body = JSON.stringify(data);
                    options.headers = {
                        'Content-Type': 'application/json',
                        ...options.headers
                    };
                }

                return fetch(url, options).then(response => {
                    return response.json().then(data => ({
                        data: data,
                        status: response.status,
                        statusText: response.statusText
                    }));
                });
            }
        };
    }

    // 智能上传分发器（根据文档设计）
    function uploadFiles(files) {
        const fileArray = Array.from(files);

        if (fileArray.length === 1) {
            // 单文件路径 - 自动分析
            uploadSingleFileWithAnalysis(fileArray[0]);
        } else if (fileArray.length > 1) {
            // 多文件路径 - 询问用户
            showMultiFileUploadDialog(fileArray);
        }
    }

    // 单文件上传并自动分析
    function uploadSingleFileWithAnalysis(file) {
        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append("files", file);
        formData.append("analysis_type", selectedAnalysisType);

        showMessage("正在上传并分析文件...", "info");

        // 显示进度
        const progressContainer = document.getElementById('progressContainer');
        const progressText = document.getElementById('progressText');
        if (progressContainer) {
            progressContainer.classList.add('show');
        }
        if (progressText) {
            progressText.textContent = '正在上传并分析...';
        }

        const httpClient = getHttpClient();
        httpClient.post("/api/analysis/start", formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            if (response.data.success) {
                const results = response.data.analysis_results || [];
                if (results.length > 0 && results[0].success) {
                    showMessage("文件上传并分析成功！", "success");
                    // 如果是首次分析，显示特殊提示
                    if (results[0].is_first_analysis) {
                        showMessage("首次分析完成，识别率100%", "info");
                    }
                } else {
                    showMessage("文件上传成功，但分析失败", "warning");
                }

                // 跳转到记录页面
                setTimeout(() => {
                    window.location.href = '/records';
                }, 1500);
            } else {
                showMessage(`上传失败: ${response.data.message}`, "error");
            }
        })
        .catch(error => {
            showMessage("上传过程中发生错误", "error");
            console.error("Upload error:", error);
        })
        .finally(() => {
            resetUploadInterface();
        });
    }

    // 多文件上传对话框
    function showMultiFileUploadDialog(files) {
        const fileCount = files.length;
        const message = `您选择了 ${fileCount} 个文件。是否在上传后自动开始分析？`;

        if (confirm(message + '\n\n点击"确定"立即分析，点击"取消"仅上传')) {
            // 用户选择立即分析
            uploadMultipleFiles(files, true);
        } else {
            // 用户选择仅上传
            uploadMultipleFiles(files, false);
        }
    }

    // 批量文件上传
    function uploadMultipleFiles(files, autoAnalyze) {
        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append("analysis_type", selectedAnalysisType);

        files.forEach((file, index) => {
            formData.append('files', file);
        });

        const message = autoAnalyze ? "正在批量上传并分析文件..." : "正在批量上传文件...";
        showMessage(message, "info");

        // 显示进度
        const progressContainer = document.getElementById('progressContainer');
        const progressText = document.getElementById('progressText');
        if (progressContainer) {
            progressContainer.classList.add('show');
        }
        if (progressText) {
            progressText.textContent = message;
        }

        const httpClient = getHttpClient();
        httpClient.post("/api/analysis/start", formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            if (response.data.success) {
                const results = response.data.analysis_results || [];
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;

                if (autoAnalyze) {
                    if (successCount === totalCount) {
                        showMessage(`批量分析完成，成功分析 ${successCount} 个文件`, "success");
                    } else {
                        showMessage(`批量分析完成，成功分析 ${successCount}/${totalCount} 个文件`, "warning");
                    }
                } else {
                    showMessage(`批量上传完成，上传 ${totalCount} 个文件`, "success");
                }

                // 跳转到记录页面
                setTimeout(() => {
                    window.location.href = '/records';
                }, 1500);
            } else {
                showMessage(`操作失败: ${response.data.message}`, "error");
            }
        })
        .catch(error => {
            showMessage("操作过程中发生错误", "error");
            console.error("Operation error:", error);
        })
        .finally(() => {
            resetUploadInterface();
        });
    }

    // 加载最近文件
    function loadRecentFiles() {
        const container = document.getElementById('recentFiles');
        if (!container) return;

        API.get('/api/records/recent', { limit: 5 })
            .then(response => {
                if (response.success && response.data.length > 0) {
                    renderRecentFiles(response.data);
                } else {
                    container.innerHTML = `
                        <div class="text-center p-4 text-muted">
                            <i class="bi bi-inbox display-4 mb-3"></i>
                            <p class="mb-0">暂无最近处理的文件</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('加载最近文件失败:', error);
                container.innerHTML = `
                    <div class="text-center p-4 text-muted">
                        <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                        <p class="mb-0">加载失败</p>
                    </div>
                `;
            });
    }

    // 渲染最近文件
    function renderRecentFiles(files) {
        const container = document.getElementById('recentFiles');
        if (!container) return;

        container.innerHTML = '';

        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.onclick = () => window.location.href = `/records/${file.id}`;

            const fileType = file.file_name.toLowerCase().includes('.pdf') ? 'pdf' : 'image';
            const iconClass = fileType === 'pdf' ? 'bi-file-pdf' : 'bi-file-image';
            const statusColor = getStatusColor(file.status);
            const statusText = getStatusText(file.status);

            fileItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="file-icon file-${fileType}">
                        <i class="bi ${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-semibold text-truncate" style="max-width: 200px;" title="${file.file_name}">
                            ${file.file_name}
                        </div>
                        <div class="small text-muted">
                            ${Utils.formatDateTime(file.created_at)}
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${statusColor}">${statusText}</span>
                        ${file.accuracy_score ? `<div class="small text-muted mt-1">${file.accuracy_score}%</div>` : ''}
                    </div>
                </div>
            `;

            container.appendChild(fileItem);
        });
    }

    // 获取状态颜色
    function getStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'processing': 'info',
            'completed': 'success',
            'failed': 'danger',
            'reviewing': 'primary'
        };
        return colors[status] || 'secondary';
    }

    // 获取状态文本
    function getStatusText(status) {
        const texts = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败',
            'reviewing': '复核中'
        };
        return texts[status] || status;
    }

    // 加载类型统计
    function loadTypeStats(days = 30) {
        const container = document.getElementById('typeStats');
        if (!container) return;

        API.get(`/api/dashboard/type-stats?days=${days}`)
            .then(response => {
                if (response.success) {
                    renderTypeStats(response.data);
                } else {
                    console.error('加载类型统计失败:', response.message);
                }
            })
            .catch(error => {
                console.error('加载类型统计失败:', error);
                // 显示模拟数据
                renderTypeStats([
                    { type: '开户文件解析', count: 245, percentage: 35.2 },
                    { type: '理财产品说明书', count: 189, percentage: 27.1 },
                    { type: '券商账户计息变更', count: 134, percentage: 19.2 },
                    { type: '账户开户场景', count: 78, percentage: 11.2 },
                    { type: '宁银理财费用变更', count: 32, percentage: 4.6 },
                    { type: '非标交易确认单解析', count: 18, percentage: 2.7 }
                ]);
            });
    }

    // 渲染类型统计
    function renderTypeStats(stats) {
        const container = document.getElementById('typeStats');
        if (!container) return;

        container.innerHTML = '';

        const colors = [
            'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
            'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
            'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
        ];

        stats.forEach((stat, index) => {
            const statCard = document.createElement('div');
            statCard.className = 'col-md-4 mb-3';

            statCard.innerHTML = `
                <div class="d-flex align-items-center p-3 rounded" style="background: ${colors[index % colors.length]}; color: white;">
                    <div class="flex-grow-1">
                        <div class="fw-semibold">${stat.type}</div>
                        <div class="small opacity-75">${stat.count} 个文件</div>
                    </div>
                    <div class="text-end">
                        <div class="h5 mb-0">${stat.percentage}%</div>
                    </div>
                </div>
            `;

            container.appendChild(statCard);
        });
    }
    
    // 加载最近执行操作
    function loadRecentOperations() {
        const operationsContainer = document.querySelector('#recentOperations .operation-list');
        const loadingElement = document.querySelector('#recentOperations .operation-loading');
        const emptyElement = document.querySelector('#recentOperations .operation-empty');
        
        if (!operationsContainer) return;
        
        // 显示加载状态
        if (loadingElement) loadingElement.classList.remove('d-none');
        if (emptyElement) emptyElement.classList.add('d-none');
        if (operationsContainer) operationsContainer.classList.add('d-none');
        
        // 调用API获取最近操作
        API.get('/api/recent-activities')
            .then(response => {
                if (response.success && response.data && response.data.length > 0) {
                    renderRecentOperations(response.data);
                    if (loadingElement) loadingElement.classList.add('d-none');
                    if (emptyElement) emptyElement.classList.add('d-none');
                    if (operationsContainer) operationsContainer.classList.remove('d-none');
                } else {
                    // 没有数据，显示空状态
                    if (loadingElement) loadingElement.classList.add('d-none');
                    if (emptyElement) emptyElement.classList.remove('d-none');
                    if (operationsContainer) operationsContainer.classList.add('d-none');
                }
            })
            .catch(error => {
                console.error('加载最近操作失败:', error);
                // 出错，显示空状态
                if (loadingElement) loadingElement.classList.add('d-none');
                if (emptyElement) emptyElement.classList.remove('d-none');
                if (operationsContainer) operationsContainer.classList.add('d-none');
            });
    }
    
    // 渲染最近操作
    function renderRecentOperations(activities) {
        const container = document.querySelector('#recentOperations .operation-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        // 操作图标映射
        const actionIcons = {
            'upload': 'bi-cloud-upload',
            'download': 'bi-download',
            'analysis': 'bi-search',
            'review': 'bi-clipboard-check',
            'login': 'bi-box-arrow-in-right',
            'logout': 'bi-box-arrow-right',
            'edit': 'bi-pencil-square',
            'delete': 'bi-trash',
            'create': 'bi-plus-circle',
            'update': 'bi-arrow-clockwise'
        };
        
        // 操作颜色映射
        const actionColors = {
            'upload': 'primary',
            'download': 'info',
            'analysis': 'success',
            'review': 'warning',
            'login': 'secondary',
            'logout': 'secondary',
            'edit': 'primary',
            'delete': 'danger',
            'create': 'success',
            'update': 'info'
        };
        
        // 操作中文描述映射
        const actionTexts = {
            'upload': '上传文件',
            'download': '下载文件',
            'analysis': '分析文件',
            'review': '复核记录',
            'login': '用户登录',
            'logout': '用户登出',
            'edit': '编辑记录',
            'delete': '删除记录',
            'create': '创建记录',
            'update': '更新记录'
        };

        activities.forEach(activity => {
            const actionBase = activity.action.split('_')[0]; // 提取基础动作类型
            const iconClass = actionIcons[actionBase] || 'bi-activity';
            const colorClass = actionColors[actionBase] || 'secondary';
            const actionText = actionTexts[actionBase] || activity.action;
            
            const time = new Date(activity.created_at).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            // 创建操作记录项
            const operationItem = document.createElement('div');
            operationItem.className = 'activity-item border-start';
            operationItem.style.borderColor = `var(--${colorClass}-color)`;
            
            // 移除悬停效果，使界面更简洁
            
            let resourceInfo = '';
            if (activity.resource) {
                const resourceName = activity.resource.length > 25 ? 
                    activity.resource.substring(0, 25) + '...' : 
                    activity.resource;
                resourceInfo = `<span class="d-block text-truncate small" title="${activity.resource}">${resourceName}</span>`;
            }
            
            let detailsInfo = '';
            if (activity.details) {
                try {
                    // 尝试解析JSON详情
                    const details = typeof activity.details === 'object' ? 
                        activity.details : JSON.parse(activity.details);
                    
                    if (details.id) {
                        detailsInfo = `<span class="badge bg-light text-dark small">ID: ${details.id}</span>`;
                    }
                    if (details.type) {
                        detailsInfo += ` <span class="badge bg-light text-dark small">${details.type}</span>`;
                    }
                } catch (e) {
                    // 解析失败则不显示详情
                }
            }
            
            operationItem.innerHTML = `
                <div class="d-flex align-items-start">
                    <div class="activity-icon me-2 d-flex align-items-center justify-content-center" 
                         style="width: 32px; height: 32px; min-width: 32px; border-radius: 50%; background: var(--${colorClass}-bg); color: var(--${colorClass}-color); font-size: 0.9rem;">
                        <i class="bi ${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <span class="fw-medium small text-nowrap me-2">${actionText}</span>
                            <small class="text-muted text-nowrap" style="font-size: 0.75rem;">${time}</small>
                        </div>
                        <div class="text-truncate">${resourceInfo}</div>
                        <div class="mt-1 text-wrap" style="font-size: 0.75rem;">${detailsInfo}</div>
                    </div>
                </div>
            `;
            
            container.appendChild(operationItem);
        });
    }
</script>
{% endblock %}