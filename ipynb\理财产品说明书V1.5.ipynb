{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from ocr_api import *\n", "from util_ai import ChatBot\n", "from utils import fn_to_markdown_v2, rag_search_neighbor, markdown_json_to_dict\n", "import re"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# 使用与lccpsms_analysis.py相同的逻辑\n", "usage_model = 'InternVL3-38B'\n", "fn = \"../大模型样例/POC脱敏材料/理财产品说明书（脱敏）/白金12M25011产品说明书.pdf\"\n", "\n", "# 使用fn_to_markdown_v2，参数与lccpsms_analysis.py保持一致\n", "markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)\n", "\n", "# 简化的参考文档提取（与lccpsms_analysis.py一致）\n", "refer_docs, refer_doc_text = rag_search_neighbor(markdown_content, keywords=[\"销售\", \"代销\", \"代理销售\"], keywords2=[\"公司\", \"银行\"])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'销售机构': ['长安银行股份有限公司']}\n"]}], "source": ["chatbot = ChatBot(\n", "    model=usage_model,  # 使用统一的模型变量\n", "    system_prompt=\"\"\"你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。\n", "    注意：请勿捏造数据，请根据实际情况输出。\n", "    请注意：\n", "    - 输出格式必须为json格式，不要输出其他内容。\n", "    - 如不存在销售机构，则输出销售机构为本银行\n", "\n", "    # 示例输出(仅供参考，请根据实际情况输出)\n", "    ```json\n", "    {\n", "      \"销售机构\": [\n", "        \"XX银行股份有限公司\",\n", "        \"XX银行股份有限公司\"\n", "      ]\n", "    }\n", "    ```\n", "    \"\"\"\n", ")\n", "\n", "# 使用与lccpsms_analysis.py相同的参数\n", "response = chatbot.chat(refer_doc_text, top_p=0.75, temperature=0.3)\n", "\n", "# 使用标准的JSON解析方法\n", "json_data = markdown_json_to_dict(response)\n", "print(json_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}